package com.rs.module.ihc.controller.admin.md.vo;

import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.RyxxVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.vo.GjApprovalTraceVO;
import com.rs.module.ihc.service.md.MedicineDeliveryApplyServiceImpl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppMedicineDeliveryApplyRespVO extends RyxxVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    @Format(service = PrisonerService.class, method = "getSimplePrisonerByJgrybm", value = "jgrybm", toBean = PrisonerVwRespVO.class, isTile = true)
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("期望送药开始日期")
    private Date expectedStartDate;
    @ApiModelProperty("期望送药结束日期")
    private Date expectedEndDate;
    @ApiModelProperty("顾送药来源")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GSYLY")
    private String drugSource;
    @Trans(type = TransType.DICTIONARY, key = "ZD_GSYYY")
    @ApiModelProperty("顾送药原因")
    private String deliveryReason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否一致")
    private Short isConsistent;
    @ApiModelProperty("不一致原因")
    private String reasonForInconsistency;
    @ApiModelProperty("药品照片URL")
    private String imgUrl;
    @ApiModelProperty("送药日期")
    private Date deliveryDate;
    @ApiModelProperty("送药备注")
    private String deliveryRemark;
    @ApiModelProperty("异常原因")
    private String exceptionReason;
    @ApiModelProperty("异常备注")
    private String exceptionRemark;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_YPGS_ZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    private Date addTime;

    @ApiModelProperty("顾送药清单")
    @Format(service = MedicineDeliveryApplyServiceImpl.class, method = "getMedicineDeliverysName", value = "id")
    private String  medicineDeliverysName;

    @ApiModelProperty("当前审批节点")
    private JSONObject currentNode;
    @ApiModelProperty("审批轨迹")
    private List<GjApprovalTraceVO> traceList;

}
