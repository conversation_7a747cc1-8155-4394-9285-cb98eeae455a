package com.rs.module.ihc.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.SupplierDO;
import com.rs.module.ihc.service.pm.SupplierService;

@Api(tags = "管理后台 - 药房管理-供应商")
@RestController
@RequestMapping("/ihc/pm/supplier")
@Validated
public class SupplierController {

    @Resource
    private SupplierService supplierService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-供应商")
    @LogRecordAnnotation(bizModule = "ihc:supplier:create", operateType = LogOperateType.CREATE, title = "创建药房管理-供应商",
    success = "创建药房管理-供应商成功", fail = "创建药房管理-供应商失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createSupplier(@Valid @RequestBody SupplierSaveReqVO createReqVO) {
        return success(supplierService.createSupplier(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-供应商")
    @LogRecordAnnotation(bizModule = "ihc:supplier:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-供应商",
    success = "更新药房管理-供应商成功", fail = "更新药房管理-供应商失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateSupplier(@Valid @RequestBody SupplierSaveReqVO updateReqVO) {
        supplierService.updateSupplier(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-供应商")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:supplier:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-供应商",
    success = "删除药房管理-供应商成功", fail = "删除药房管理-供应商失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteSupplier(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           supplierService.deleteSupplier(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-供应商")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:supplier:get", operateType = LogOperateType.QUERY, title = "获取药房管理-供应商", bizNo = "{{#id}}", success = "获取药房管理-供应商成功", fail = "获取药房管理-供应商失败", extraInfo = "{{#id}}")
    public CommonResult<SupplierRespVO> getSupplier(@RequestParam("id") String id) {
        SupplierDO supplier = supplierService.getSupplier(id);
        return success(BeanUtils.toBean(supplier, SupplierRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-供应商分页")
    @LogRecordAnnotation(bizModule = "ihc:supplier:page", operateType = LogOperateType.QUERY, title = "获得药房管理-供应商分页",
    success = "获得药房管理-供应商分页成功", fail = "获得药房管理-供应商分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<SupplierRespVO>> getSupplierPage(@Valid SupplierPageReqVO pageReqVO) {
        PageResult<SupplierDO> pageResult = supplierService.getSupplierPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SupplierRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-供应商列表")
    @LogRecordAnnotation(bizModule = "ihc:supplier:list", operateType = LogOperateType.QUERY, title = "获得药房管理-供应商列表",
    success = "获得药房管理-供应商列表成功", fail = "获得药房管理-供应商列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<SupplierRespVO>> getSupplierList(@Valid SupplierListReqVO listReqVO) {
    List<SupplierDO> list = supplierService.getSupplierList(listReqVO);

        return success(BeanUtils.toBean(list, SupplierRespVO.class));
    }

}
